<template>
  <div class="box">
    <div id="top-container" class="pb-15">
      <Filter @search="handleSearch" @toggleShowMore="getTableHeight" />

      <!-- 操作按钮区域 -->
      <div class="action-bar mt-15">
        <el-button type="primary" @click="openEnterCooldownDialog"> 主动进入冷静期 </el-button>
      </div>
    </div>

    <el-table
      ref="table"
      :data="list"
      border
      :max-height="maxTableHeight"
      v-loading="loading"
      @sort-change="handleSortTable"
    >
      <el-table-column
        prop="id"
        label="注销日志ID"
        align="center"
        min-width="120"
        sortable
      ></el-table-column>

      <el-table-column label="用户信息" align="center" min-width="200">
        <template #default="{ row }">
          <div>
            <div>{{ row.name }}</div>
            <div class="text-gray-500 text-sm">{{ row.mobileMasked }}</div>
            <div class="text-gray-500 text-sm">{{ row.emailMasked }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="注销状态" align="center" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="cancelReasonTypeText"
        label="注销原因"
        align="center"
        min-width="200"
      ></el-table-column>

      <el-table-column
        prop="applyTimeFormat"
        label="申请时间"
        align="center"
        min-width="160"
        sortable
      ></el-table-column>

      <el-table-column
        prop="cooldownEndTimeFormat"
        label="冷静期结束时间"
        align="center"
        min-width="160"
        sortable
      ></el-table-column>

      <el-table-column label="剩余冷静期" align="center" min-width="120">
        <template #default="{ row }">
          <span :class="{ 'text-red-500': row.remainingCooldownDays <= 0 }">
            {{ row.remainingCooldownDays }}天
          </span>
        </template>
      </el-table-column>

      <el-table-column
        prop="smsStatusText"
        label="短信状态"
        align="center"
        min-width="140"
      ></el-table-column>

      <el-table-column prop="ip" label="IP地址" align="center" min-width="120"></el-table-column>

      <el-table-column label="操作" align="center" width="380" fixed="right">
        <template #default="{ row }">
          <div class="flex jc-center ai-center gap-2">
            <el-button size="small" type="primary" @click="viewDetail(row.id)">
              查看详情
            </el-button>
            <el-button
              size="small"
              type="warning"
              :disabled="row.status == 3"
              @click="submitCancel(row.id)"
            >
              提交注销
            </el-button>
            <el-button
              v-if="row.status === 1"
              size="small"
              type="info"
              @click="handleCancelCooldown(row.id)"
            >
              取消冷静期
            </el-button>
            <el-button
              v-if="row.status === 1"
              size="small"
              type="danger"
              @click="manualCancel(row.id)"
            >
              人工执行注销
            </el-button>
          </div>
        </template>
      </el-table-column>

      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-end ai-center"
      style="flex-shrink: 0"
    >
      <Pagination
        v-if="pagination.total > 0"
        :total="pagination.total"
        :page="formData.page"
        @change="handlePaginationChange"
      />
    </div>
  </div>
  <DetailDialog ref="detailDialog" @refresh="getList" />
  <EnterCooldownDialog ref="enterCooldownDialog" @refresh="getList" />
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Filter from './components/Filter.vue'
import Pagination from '/@/components/base/paging.vue'
import DetailDialog from './components/DetailDialog.vue'
import EnterCooldownDialog from './components/EnterCooldownDialog.vue'

import { getResumeCancelList, manualCancelResume, cancelCooldown } from '/@/api/resumeCancel'

defineOptions({ name: 'resumeCancelList' })

interface FormData {
  cancelLogId: string
  mobile: string
  name: string
  email: string
  status: string
  cancelReasonType: string
  smsStatus: string
  applyTimeStart: string
  applyTimeEnd: string
  cooldownEndTimeStart: string
  cooldownEndTimeEnd: string
  completeTimeStart: string
  completeTimeEnd: string
  adminId: string
  ip: string
  sortField: string
  sortOrder: string
  page: number
  pageSize: number
}

const table = ref()
const detailDialog = ref()
const enterCooldownDialog = ref()
const maxTableHeight = ref(450)

const loading = ref(false)
const list = ref([])

// 表单数据
const formData = reactive<FormData>({
  cancelLogId: '',
  mobile: '',
  name: '',
  email: '',
  status: '',
  cancelReasonType: '',
  smsStatus: '',
  applyTimeStart: '',
  applyTimeEnd: '',
  cooldownEndTimeStart: '',
  cooldownEndTimeEnd: '',
  completeTimeStart: '',
  completeTimeEnd: '',
  adminId: '',
  ip: '',
  sortField: '',
  sortOrder: '',
  page: 1,
  pageSize: 20
})

// 分页数据
const pagination = reactive({
  total: 0,
  page: 1,
  limit: 20
})

// 获取列表数据
const getList = async () => {
  loading.value = true

  const params = {}
  Object.keys(formData).forEach((key: string) => {
    const value = formData[key]
    if (value !== '' && value !== null && value !== undefined) {
      params[key] = Array.isArray(value) ? value.join() : value
    }
  })

  try {
    const { list: dataList, pages } = await getResumeCancelList(params)
    list.value = dataList
    pagination.total = pages.total
    pagination.page = pages.page
    pagination.limit = pages.pageSize
  } catch (error) {
  } finally {
    loading.value = false
    getTableHeight()
  }
}

// 搜索处理
const handleSearch = (searchData: any) => {
  Object.assign(formData, searchData)
  formData.page = 1
  pagination.page = 1
  getList()
}

// 查看详情
const viewDetail = (id: number) => {
  detailDialog.value.open(id)
}

// 打开主动进入冷静期对话框
const openEnterCooldownDialog = () => {
  enterCooldownDialog.value.open()
}

// 取消冷静期
const handleCancelCooldown = (cancelLogId: number) => {
  ElMessageBox.confirm(
    '确定要取消该用户的冷静期吗？\n\n注意：\n• 用户的注销申请将被撤回\n• 系统将恢复预处理操作的影响\n• 此操作将记录操作日志\n\n请谨慎操作！',
    '确认取消冷静期',
    {
      confirmButtonText: '确定取消',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false
    }
  ).then(async () => {
    try {
      await cancelCooldown({ cancelLogId })
      ElMessage.success('冷静期已成功取消')
      getList() // 刷新列表
    } catch (error: any) {
      console.error('取消冷静期失败:', error)
      ElMessage.error(error.message || '操作失败，请重试')
    }
  })
}

// 提交注销
const submitCancel = (id: number) => {
  ElMessageBox.confirm(
    '确定要提交注销申请吗？\n\n注意：\n• 此操作将立即处理注销申请\n• 操作完成后无法撤销\n• 用户数据将被永久删除\n\n请谨慎操作！',
    '确认提交注销',
    {
      confirmButtonText: '确定提交',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false
    }
  ).then(async () => {
    try {
      await manualCancelResume({ cancelLogId: id })
      ElMessage.success('注销申请提交成功')
      getList() // 刷新列表
    } catch (error) {
      console.error('提交注销失败:', error)
    }
  })
}

// 人工执行注销
const manualCancel = (id: number) => {
  ElMessageBox.confirm('确定要执行注销操作吗？此操作不可撤销！', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await manualCancelResume({ cancelLogId: id })
      ElMessage.success('注销操作执行成功')
      getList() // 刷新列表
    } catch (error) {
      console.error('人工执行注销失败:', error)
    }
  })
}

// 分页变化
const handlePaginationChange = (page: any) => {
  formData.page = page.page
  formData.pageSize = page.limit
  pagination.page = page.page
  pagination.limit = page.limit
  getList()
}

// 表格排序
const handleSortTable = (sort: any) => {
  if (sort.prop) {
    formData.sortField = sort.prop
    formData.sortOrder = sort.order === 'ascending' ? 'ASC' : 'DESC'
  } else {
    formData.sortField = ''
    formData.sortOrder = ''
  }
  handleSearch(formData)
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'warning' // 申请中
    case 2:
      return 'info' // 已撤回
    case 3:
      return 'success' // 已完成
    default:
      return ''
  }
}

// 计算表格高度
const getTableHeight = () => {
  nextTick(() => {
    const topContainer = document.getElementById('top-container')
    if (topContainer) {
      const topHeight = topContainer.offsetHeight
      maxTableHeight.value = window.innerHeight - topHeight - 200
    }
  })
}

// 初始化
onBeforeMount(() => {
  getList()
})

// onActivated(() => {
//   if (!isFirst.value) {
//     getList()
//   }
//   isFirst.value = false
// })
</script>

<style scoped lang="scss">
:deep() {
  @import '/@/theme/tableScrollBar.scss';
}

.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.amount {
  margin: 20px 0 0px;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-sm {
  font-size: 12px;
}

.text-red-500 {
  color: #ef4444;
}

.gap-2 {
  gap: 8px;
}

.action-bar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mt-15 {
  margin-top: 15px;
}
</style>
