# 求职者注销冷静期管理接口文档

## 概述
本文档描述了求职者注销冷静期管理相关的接口，包括主动进入冷静期和取消冷静期功能。

## 接口列表

### 1. 主动进入冷静期接口

#### 接口信息
- **接口名称**: 主动进入冷静期（运营后台操作）
- **接口地址**: `/admin/resume-cancel/enter-cooldown`
- **请求方式**: POST
- **接口描述**: 允许运营后台直接让用户进入冷静期状态，开始注销申请流程，无需短信验证码

#### 请求参数
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| resumeId | Number | 是 | 12345 | 简历ID |
| cancelReasonType | Number | 是 | 1 | 注销原因类型：1=已找到工作，2=换手机号，3=注册错误，4=多个账号，5=隐私担忧，6=不想接收邮件，99=其他 |
| cancelReasonDetail | String | 否 | 找到满意的工作 | 注销原因详细说明（选择其他时必填） |

#### Apifox导入格式
```
resumeId,Number,是,12345,-,简历ID
cancelReasonType,Number,是,1,-,注销原因类型：1=已找到工作/2=换手机号/3=注册错误/4=多个账号/5=隐私担忧/6=不想接收邮件/99=其他
cancelReasonDetail,String,否,找到满意的工作,-,注销原因详细说明（选择其他时必填）
```

#### 返回数据格式
**成功响应**:
```json
{
  "msg": "已成功进入冷静期",
  "result": 1,
  "data": {
    "cancelLogId": 123,
    "cooldownEndTime": "2023-12-08 23:59:59",
    "adminId": 1001
  }
}
```

**失败响应**:
```json
{
  "msg": "简历ID不能为空",
  "result": 0,
  "data": null
}
```

#### 返回字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| cancelLogId | Number | 注销日志ID |
| cooldownEndTime | String | 冷静期结束时间 |
| adminId | Number | 操作的管理员ID |

---

### 2. 取消冷静期接口

#### 接口信息
- **接口名称**: 取消冷静期（运营后台操作）
- **接口地址**: `/admin/resume-cancel/cancel-cooldown`
- **请求方式**: POST
- **接口描述**: 允许运营后台直接取消用户的冷静期状态，撤回注销申请，无需短信验证码

#### 请求参数
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| cancelLogId | Number | 是 | 123 | 注销日志ID |

#### Apifox导入格式
```
cancelLogId,Number,是,123,-,注销日志ID
```

#### 返回数据格式
**成功响应**:
```json
{
  "msg": "冷静期已成功取消",
  "result": 1,
  "data": {
    "success": true,
    "message": "注销申请已成功撤回",
    "withdrawTime": "2023-12-01 14:30:00",
    "adminId": 1001
  }
}
```

**失败响应**:
```json
{
  "msg": "注销申请记录不存在",
  "result": 0,
  "data": null
}
```

#### 返回字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 操作是否成功 |
| message | String | 操作结果消息 |
| withdrawTime | String | 撤回时间 |
| adminId | Number | 操作的管理员ID |

---

## 业务规则说明

### 冷静期规则
1. **冷静期时长**: 申请注销后进入7天冷静期（含申请当日）
2. **冷静期内操作**: 用户可以在冷静期内撤回注销申请
3. **冷静期结束**: 冷静期结束后系统自动执行注销操作
4. **运营后台特权**: 运营后台可以在任何时候撤回注销申请，不受冷静期时间限制

### 注销原因类型说明
- `1`: 已找到新工作，以后不再打算找工作
- `2`: 换手机号了，重新注册
- `3`: 注册企业账号，误操作为个人账号
- `4`: 已有多个账号，想注销一个
- `5`: 担心隐私泄露
- `6`: 不想接收到邀约邮件
- `99`: 其他原因（需要填写详细说明）

### 状态说明
- **申请中**: 用户已提交注销申请，正在冷静期内
- **已撤回**: 用户在冷静期内撤回了注销申请
- **已完成**: 冷静期结束，注销操作已执行完成

## 错误码说明

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 简历ID不能为空 | 未传入resumeId参数 | 检查请求参数 |
| 注销原因类型不能为空 | 未传入cancelReasonType参数 | 检查请求参数 |
| 注销日志ID不能为空 | 未传入cancelLogId参数 | 检查请求参数 |
| 简历不存在 | 传入的简历ID无效 | 检查简历ID是否正确 |
| 用户不存在 | 简历对应的用户不存在 | 检查用户状态 |
| 注销申请记录不存在 | 传入的注销日志ID无效 | 检查注销日志ID是否正确 |
| 该用户已有进行中的注销申请，请勿重复提交 | 用户已有未完成的注销申请 | 等待当前申请处理完成 |
| 注销申请状态异常，无法撤回 | 注销申请不是申请中状态 | 检查注销申请当前状态 |

## 注意事项

1. **运营后台特权**: 运营后台操作无需短信验证码，直接通过简历ID或注销日志ID操作
2. **事务处理**: 所有操作都在数据库事务中执行，确保数据一致性
3. **日志记录**: 所有操作都会记录详细的操作日志，包括操作的管理员ID
4. **权限控制**: 运营后台具有最高权限，可以操作任何用户的注销申请
5. **时间限制**: 运营后台撤回操作不受冷静期时间限制
6. **预处理操作**: 进入冷静期时会立即执行预处理操作（如退出人才库、关闭通知等）
7. **恢复操作**: 取消冷静期时会自动恢复预处理操作的影响
8. **短信通知**: 运营后台操作不会发送短信通知给用户

## 相关接口

- 发送注销短信验证码接口
- 获取注销申请列表接口
- 获取注销申请详情接口
- 人工执行注销接口
