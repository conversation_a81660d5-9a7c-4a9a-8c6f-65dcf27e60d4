# 冷静期管理接口测试用例

## 测试环境准备

### 前置条件
1. 测试环境已部署最新代码
2. 数据库中存在有效的测试用户和简历数据
3. 短信服务正常运行
4. 准备有效的用户 JWT token

### 测试数据准备
- 测试用户ID: `12345`
- 测试简历ID: `67890`
- 测试手机号: `13800138000`
- 有效的短信验证码: `123456`
- 有效的用户token: `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...`

## 接口测试用例

### 1. 主动进入冷静期接口测试

#### 测试用例 1.1: 正常进入冷静期
**测试目标**: 验证运营后台能够正常让用户进入冷静期

**请求信息**:
- URL: `POST /admin/resume-cancel/enter-cooldown`
- Headers: `Content-Type: application/json`
- Body:
```json
{
  "resumeId": 67890,
  "cancelReasonType": 1,
  "cancelReasonDetail": "已找到满意的工作"
}
```

**预期结果**:
```json
{
  "msg": "已成功进入冷静期",
  "result": 1,
  "data": {
    "cancelLogId": 123,
    "cooldownEndTime": "2023-12-08 23:59:59",
    "adminId": 1001
  }
}
```

**验证点**:
- 返回状态码 200
- result 字段为 1
- data 包含 cancelLogId、cooldownEndTime 和 adminId
- 数据库中创建了对应的注销日志记录，包含管理员ID

---

#### 测试用例 1.2: 缺少必填参数
**测试目标**: 验证参数验证功能

**请求信息**:
- URL: `POST /admin/resume-cancel/enter-cooldown`
- Body:
```json
{
  "cancelReasonType": 1
}
```

**预期结果**:
```json
{
  "msg": "简历ID不能为空",
  "result": 0,
  "data": null
}
```

**验证点**:
- 返回状态码 200
- result 字段为 0
- msg 包含正确的错误信息

---

#### 测试用例 1.3: 重复申请注销
**测试目标**: 验证重复申请的处理

**前置条件**: 用户已有进行中的注销申请

**请求信息**:
- URL: `POST /admin/resume-cancel/enter-cooldown`
- Body:
```json
{
  "resumeId": 67890,
  "cancelReasonType": 1,
  "smsCode": "123456"
}
```

**预期结果**:
```json
{
  "msg": "您已有进行中的注销申请，请勿重复提交",
  "result": 0,
  "data": null
}
```

---

#### 测试用例 1.4: 无效的简历ID
**测试目标**: 验证简历ID有效性检查

**请求信息**:
- URL: `POST /admin/resume-cancel/enter-cooldown`
- Body:
```json
{
  "resumeId": 999999,
  "cancelReasonType": 1,
  "smsCode": "123456"
}
```

**预期结果**:
```json
{
  "msg": "简历不存在",
  "result": 0,
  "data": null
}
```

---

### 2. 取消冷静期接口测试

#### 测试用例 2.1: 正常取消冷静期
**测试目标**: 验证运营后台能够正常取消冷静期

**前置条件**: 存在进行中的注销申请

**请求信息**:
- URL: `POST /admin/resume-cancel/cancel-cooldown`
- Body:
```json
{
  "cancelLogId": 123
}
```

**预期结果**:
```json
{
  "msg": "冷静期已成功取消",
  "result": 1,
  "data": {
    "success": true,
    "message": "注销申请已成功撤回",
    "withdrawTime": "2023-12-01 14:30:00",
    "adminId": 1001
  }
}
```

**验证点**:
- 返回状态码 200
- result 字段为 1
- data 包含撤回成功信息和管理员ID
- 数据库中注销日志状态更新为已撤回

---

#### 测试用例 2.2: 缺少cancelLogId参数
**测试目标**: 验证cancelLogId参数验证

**请求信息**:
- URL: `POST /admin/resume-cancel/cancel-cooldown`
- Body:
```json
{}
```

**预期结果**:
```json
{
  "msg": "注销日志ID不能为空",
  "result": 0,
  "data": null
}
```

---

#### 测试用例 2.3: 无效的注销日志ID
**测试目标**: 验证注销日志ID有效性检查

**请求信息**:
- URL: `POST /admin/resume-cancel/cancel-cooldown`
- Body:
```json
{
  "cancelLogId": 999999
}
```

**预期结果**:
```json
{
  "msg": "注销申请记录不存在",
  "result": 0,
  "data": null
}
```

---

#### 测试用例 2.4: 注销申请状态异常
**测试目标**: 验证非申请中状态的处理

**前置条件**: 注销申请状态不是申请中（如已完成或已撤回）

**请求信息**:
- URL: `POST /admin/resume-cancel/cancel-cooldown`
- Body:
```json
{
  "cancelLogId": 123
}
```

**预期结果**:
```json
{
  "msg": "注销申请状态异常，无法撤回",
  "result": 0,
  "data": null
}
```

---

#### 测试用例 2.5: 冷静期已结束仍可撤回
**测试目标**: 验证运营后台可以在冷静期结束后撤回

**前置条件**: 注销申请冷静期已结束但状态仍为申请中

**请求信息**:
- URL: `POST /admin/resume-cancel/cancel-cooldown`
- Body:
```json
{
  "cancelLogId": 123
}
```

**预期结果**:
```json
{
  "msg": "冷静期已成功取消",
  "result": 1,
  "data": {
    "success": true,
    "message": "注销申请已成功撤回",
    "withdrawTime": "2023-12-01 14:30:00",
    "adminId": 1001
  }
}
```

## 集成测试场景

### 场景1: 完整的冷静期流程
1. 用户申请进入冷静期
2. 验证冷静期状态
3. 用户取消冷静期
4. 验证状态恢复正常

### 场景2: 冷静期自然结束流程
1. 用户申请进入冷静期
2. 等待冷静期结束（或手动修改数据库时间）
3. 验证系统自动执行注销

## 性能测试

### 并发测试
- 同一用户并发申请注销
- 多用户同时申请注销
- 验证数据一致性

### 压力测试
- 大量用户同时操作
- 验证系统响应时间
- 监控数据库性能

## 数据验证

### 数据库状态验证
1. **进入冷静期后**:
   - `resume_cancel_log` 表新增记录
   - `member` 表 `cancel_status` 更新为注销中
   - 相关预处理操作已执行

2. **取消冷静期后**:
   - `resume_cancel_log` 表状态更新为已撤回
   - `member` 表 `cancel_status` 恢复为正常
   - 预处理操作已恢复

### 业务逻辑验证
- 冷静期时间计算正确
- 短信验证码验证有效
- 用户权限验证正确
- 状态流转符合业务规则

## 测试工具推荐

### API测试工具
- **Postman**: 适合手动测试和简单自动化
- **Apifox**: 国产工具，支持接口文档和测试
- **JMeter**: 适合性能测试和压力测试

### 数据库工具
- **MySQL Workbench**: 查看数据库状态变化
- **Navicat**: 数据对比和验证

## 测试报告模板

### 测试结果记录
| 测试用例 | 执行结果 | 实际结果 | 预期结果 | 是否通过 | 备注 |
|----------|----------|----------|----------|----------|------|
| 1.1 正常进入冷静期 | PASS | 符合预期 | 符合预期 | ✓ | - |
| 1.2 缺少必填参数 | PASS | 符合预期 | 符合预期 | ✓ | - |
| ... | ... | ... | ... | ... | ... |

### 问题记录
- 发现的bug及其严重程度
- 性能问题及优化建议
- 用户体验问题及改进建议
